import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/rewardController/rewardController.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/httpRequest.dart';

class RewardCurrencyController extends GetxController {
  // Observable for selected reward currency
  RxString selectedRewardCurrency = 'like'.obs;

  // Observable for currency change permission (24-hour delay logic)
  RxBool canChangeCurrency = true.obs;

  // Observable for loading state
  RxBool isLoading = false.obs;

  // Observable for API response data
  RxMap<String, dynamic> apiResponseData = <String, dynamic>{}.obs;

  // Profile controller to get user identifier (phone number)
  late ProfileController _profileController;

  // Mockup API endpoint
  static const String _apiEndpoint = 'https://n8n-ags.agilesoftgroup.com/webhook/16355fa2-8dfc-4dd8-8007-3d973c884aaa';

  // List of reward currencies
  final List<Map<String, String>> rewardCurrencies = const [
    {"key": "like", "label": "LIKE"},
    {"key": "btc", "label": "BTC - Bitcoin"},
    {"key": "gold", "label": "(ทอง) กรัม"},
  ];

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  /// Initialize controller and check 24-hour delay on app startup
  Future<void> _initializeController() async {
    try {
      // Get ProfileController instance
      _profileController = Get.find<ProfileController>();

      // Load saved currency from local storage
      loadSavedRewardCurrency();

      // Check 24-hour delay from API on app startup
      await _checkCurrencyChangePermissionFromAPI();

    } catch (e) {
      print('Error initializing RewardCurrencyController: $e');
      // If ProfileController is not found, show error
      Get.snackbar(
        'Error',
        'Profile information not available. Please restart the app.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Load saved reward currency from storage
  void loadSavedRewardCurrency() {
    final savedCurrency = Storage.get<String>('selected_reward_currency');
    if (savedCurrency != null && savedCurrency.isNotEmpty) {
      // Check if the saved currency exists in our reward currencies list
      final exists = rewardCurrencies.any((currency) => currency['key'] == savedCurrency);
      if (exists) {
        selectedRewardCurrency.value = savedCurrency;
      } else {
        // Default to LIKE if saved currency doesn't exist
        selectedRewardCurrency.value = 'like';
        saveRewardCurrency('like');
      }
    } else {
      // Default to LIKE if no saved currency
      selectedRewardCurrency.value = 'like';
      saveRewardCurrency('like');
    }
  }

  /// Get phone number from ProfileController
  String? _getPhoneNumber() {
    try {
      // Use phone number as unique identifier
      final phoneNumber = _profileController.phoneNumber.value;
      if (phoneNumber.isNotEmpty && phoneNumber != '..loading') {
        return phoneNumber;
      }
      return null;
    } catch (e) {
      print('Error getting phone number: $e');
      return null;
    }
  }

  /// Show loading dialog using Get.dialog
  void _showLoadingDialog() {
    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // Prevent dismissal by back button
        child: AlertDialog(
          backgroundColor: const Color(0xff1e1d34),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
              SizedBox(height: 16),
              Text(
                'Loading...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false, // Prevent dismissal by tapping outside
    );
  }

  /// Hide loading dialog
  void _hideLoadingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Call API to check currency change permission on app startup
  /// API Endpoint: https://hahaMockLebro
  /// Request Body: { "phone": "0123456789" }
  /// Response: { "isOver": false, "timestamp": "202x-xx-25T00:00:53.976Z", "now": "202x-xx-25T00:00:59.965Z" }
  Future<void> _checkCurrencyChangePermissionFromAPI() async {
    try {
      isLoading.value = true;

      // Get phone number from ProfileController
      final phoneNumber = _getPhoneNumber();
      if (phoneNumber == null || phoneNumber.isEmpty) {
        print('No phone number found');
        canChangeCurrency.value = true;
        return;
      }

      // Prepare request body (startup check doesn't include currency)
      final requestBody = {
        'phone': phoneNumber,
      };

      print('Calling API on startup: $_apiEndpoint with phone: $phoneNumber');

      // Call the mockup API
      final response = await AppApi.post(_apiEndpoint, requestBody);

      print('API Response on startup: $response');

      // Store API response data
      apiResponseData.value = response;

      // Handle API response
      if (response != null && response.containsKey('isOver')) {
        final isOver = response['isOver'] as bool;

        if (isOver) {
          // User can change currency (24 hours have passed or no previous data)
          canChangeCurrency.value = true;
          print('Currency change allowed on startup');
        } else {
          // User cannot change currency (less than 24 hours)
          canChangeCurrency.value = false;
          final timestamp = response['timestamp'] ?? '';
          final now = response['now'] ?? '';
          print('Currency change blocked on startup. Last change: $timestamp, Current time: $now');
        }
      } else {
        // If API response is invalid, allow currency change to prevent blocking user
        canChangeCurrency.value = true;
        print('Invalid API response on startup, allowing currency change');
      }

    } catch (e) {
      print('Error calling API on startup: $e');
      // On error, allow currency change to prevent blocking user
      canChangeCurrency.value = true;

      // Show error to user
      Get.snackbar(
        'Network Error',
        'Unable to check currency change restrictions. Please check your internet connection.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Show 24-hour restriction popup using Get.dialog
  void _show24HourRestrictionPopup() {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff1e1d34),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.access_time,
              color: Colors.orange,
              size: 24,
            ),
            SizedBox(width: 8),
            Text(
              'Currency Change Restricted',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          'You can change your currency after 24 hours.',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'OK',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false, // Prevent dismissal by tapping outside
    );
  }

  /// Save reward currency with API validation
  /// This method calls the API with phone number and selected currency
  /// API now checks if selected currency matches database and avoids updating if same
  Future<void> saveRewardCurrency(String currencyKey) async {
    try {
      // Show loading dialog
      // _showLoadingDialog();

      // Get phone number from ProfileController
      final phoneNumber = _getPhoneNumber();
      if (phoneNumber == null || phoneNumber.isEmpty) {
        _hideLoadingDialog();
        Get.snackbar(
          'Error',
          'Phone number not available. Please restart the app.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // Prepare request body with phone number and selected currency
      final requestBody = {
        'phone': phoneNumber,
        'currency': currencyKey, // Include selected currency in request
      };

      print('Checking currency change permission for phone: $phoneNumber, currency: $currencyKey');

      // Call the mockup API to check permission and update if allowed
      final response = await AppApi.post(_apiEndpoint, requestBody);

      print('API Response for currency change: $response');

      // Store API response data
      apiResponseData.value = response;

      // Handle API response
      if (response != null && response.containsKey('isOver')) {
        final isOver = response['isOver'] as bool;

        if (isOver) {
          // User can change currency - proceed with the change
          // API has already updated the database if currency was different
          selectedRewardCurrency.value = currencyKey;
          Storage.save('selected_reward_currency', currencyKey);

          // Update permission state based on whether this was a new change or same currency
          // If same currency, user can still change immediately
          // If different currency, block further changes for 24 hours
          canChangeCurrency.value = true; // Allow immediate re-selection of same currency

          // Show success message
          final currencyLabel = _getCurrencyLabel(currencyKey);
          Get.snackbar(
            'Success',
            'Reward currency updated to $currencyLabel',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: Duration(seconds: 2),
          );

          update();
          print('Currency successfully changed to: $currencyKey');

        } else {
          // User cannot change currency - show restriction popup
          _show24HourRestrictionPopup();
          print('Currency change blocked by 24-hour restriction');
        }
      } else {
        // Invalid API response
        Get.snackbar(
          'Error',
          'Invalid response from server. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }

    } catch (e) {
      print('Error saving reward currency: $e');

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to update reward currency. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      // Hide loading dialog
      _hideLoadingDialog();
    }
  }

  /// Get currency label by key
  String _getCurrencyLabel(String key) {
    final currency = rewardCurrencies.firstWhere(
          (currency) => currency['key'] == key,
      orElse: () => {'label': key.toUpperCase()},
    );
    return currency['label'] ?? key.toUpperCase();
  }

  // Get current reward currency label
  String getCurrentRewardCurrencyLabel() {
    final currency = rewardCurrencies.firstWhere(
          (currency) => currency['key'] == selectedRewardCurrency.value,
      orElse: () => rewardCurrencies[0],
    );
    return currency['label'] ?? 'LIKE';
  }

  // Get reward currency by key
  Map<String, String>? getRewardCurrencyByKey(String key) {
    try {
      return rewardCurrencies.firstWhere((currency) => currency['key'] == key);
    } catch (e) {
      return null;
    }
  }

  // Check if a currency key is valid
  bool isValidRewardCurrency(String key) {
    return rewardCurrencies.any((currency) => currency['key'] == key);
  }

  /// Manual refresh of currency change permission status
  /// This can be used for testing or manual refresh
  Future<void> refreshCurrencyChangePermission() async {
    await _checkCurrencyChangePermissionFromAPI();
  }

  /// Get the last API response data
  Map<String, dynamic> getLastAPIResponse() {
    return Map<String, dynamic>.from(apiResponseData.value);
  }

  /// Check if API has been called at least once
  bool hasAPIBeenCalled() {
    return apiResponseData.value.isNotEmpty;
  }

  /// Get formatted timestamp from API response
  String getFormattedTimestamp() {
    try {
      final timestamp = apiResponseData.value['timestamp'] as String?;
      if (timestamp != null && timestamp.isNotEmpty) {
        final dateTime = DateTime.parse(timestamp);
        return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  /// Get formatted current time from API response
  String getFormattedCurrentTime() {
    try {
      final now = apiResponseData.value['now'] as String?;
      if (now != null && now.isNotEmpty) {
        final dateTime = DateTime.parse(now);
        return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  /// Alternative method to save currency using ModalProgressHUD loading indicator
  /// This method uses isLoading.value instead of Get.dialog for loading indication
  /// Use this when RewardCurrencyPage has ModalProgressHUD wrapper
  // Future<void> saveRewardCurrencyWithModalHUD(String currencyKey) async {
  //   try {
  //     // Set loading state (ModalProgressHUD will show automatically)
  //     isLoading.value = true;
  //
  //     // Get phone number from ProfileController
  //     final phoneNumber = _getPhoneNumber();
  //     if (phoneNumber == null || phoneNumber.isEmpty) {
  //       Get.snackbar(
  //         'Error',
  //         'Phone number not available. Please restart the app.',
  //         snackPosition: SnackPosition.BOTTOM,
  //         backgroundColor: Colors.red,
  //         colorText: Colors.white,
  //       );
  //       return;
  //     }
  //
  //     // Prepare request body with phone number and selected currency
  //     final requestBody = {
  //       'phone': phoneNumber,
  //       'currency': currencyKey, // Include selected currency in request
  //     };
  //
  //     print('Checking currency change permission for phone: $phoneNumber, currency: $currencyKey');
  //
  //     // Call the mockup API to check permission and update if allowed
  //     final response = await AppApi.post(_apiEndpoint, requestBody);
  //
  //     print('API Response for currency change: $response');
  //
  //     // Store API response data
  //     apiResponseData.value = response;
  //
  //     // Handle API response
  //     if (response != null && response.containsKey('isOver')) {
  //       final isOver = response['isOver'] as bool;
  //
  //       if (isOver) {
  //         // User can change currency - proceed with the change
  //         // API has already updated the database if currency was different
  //         selectedRewardCurrency.value = currencyKey;
  //         Storage.save('selected_reward_currency', currencyKey);
  //
  //         // Update permission state
  //         canChangeCurrency.value = true; // Allow immediate re-selection of same currency
  //
  //         // Show success message
  //         final currencyLabel = _getCurrencyLabel(currencyKey);
  //         Get.snackbar(
  //           'Success',
  //           'Reward currency updated to $currencyLabel',
  //           snackPosition: SnackPosition.BOTTOM,
  //           backgroundColor: Colors.green,
  //           colorText: Colors.white,
  //           duration: Duration(seconds: 2),
  //         );
  //
  //         update();
  //         print('Currency successfully changed to: $currencyKey');
  //
  //       } else {
  //         // User cannot change currency - show restriction popup
  //         _show24HourRestrictionPopup();
  //         print('Currency change blocked by 24-hour restriction');
  //       }
  //     } else {
  //       // Invalid API response
  //       Get.snackbar(
  //         'Error',
  //         'Invalid response from server. Please try again.',
  //         snackPosition: SnackPosition.BOTTOM,
  //         backgroundColor: Colors.red,
  //         colorText: Colors.white,
  //       );
  //     }
  //
  //   } catch (e) {
  //     print('Error saving reward currency: $e');
  //
  //     // Show error message
  //     Get.snackbar(
  //       'Error',
  //       'Failed to update reward currency. Please try again.',
  //       snackPosition: SnackPosition.BOTTOM,
  //       backgroundColor: Colors.red,
  //       colorText: Colors.white,
  //     );
  //   } finally {
  //     // Clear loading state (ModalProgressHUD will hide automatically)
  //     isLoading.value = false;
  //   }
  // }
  Future<void> saveRewardCurrencyWithModalHUD(String currencyKey) async {
    isLoading.value = true; // ย้ายออกมานอก try เพื่อ ensure เสมอ
    try {
      final phoneNumber = _getPhoneNumber();
      if (phoneNumber == null || phoneNumber.isEmpty) {
        throw Exception('Phone number not available');
      }

      final requestBody = {
        'phone': phoneNumber,
        'currency': currencyKey,
      };

      print('Checking currency change permission for phone: $phoneNumber, currency: $currencyKey');

      final response = await AppApi.post(_apiEndpoint, requestBody);
      print('API Response for currency change: $response');

      apiResponseData.value = response;

      if (response != null && response.containsKey('isOver')) {
        final isOver = response['isOver'] as bool;

        if (isOver) {
          selectedRewardCurrency.value = currencyKey;
          Storage.save('selected_reward_currency', currencyKey);
          canChangeCurrency.value = true;

          final currencyLabel = _getCurrencyLabel(currencyKey);

          await FirebaseFirestore.instance
              .collection('currency_selection_logs')
              .doc(phoneNumber)
              .set({
            'currency': currencyLabel,
            'key': currencyKey,
            'timestamp': Timestamp.now(),
          }, SetOptions(merge: true));

          Get.snackbar(
            'Success',
            'Reward currency updated to $currencyLabel',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: Duration(seconds: 2),
          );

          update();
          print('Currency successfully changed to: $currencyKey');
          Get.find<RewardController>().getNewReward();
        } else {
          _show24HourRestrictionPopup();
          print('Currency change blocked by 24-hour restriction');
        }
      } else {
        throw Exception('Invalid response from server');
      }
    } catch (e) {
      print('❌ Error saving reward currency: $e');
      Get.snackbar(
        'Error',
        'Failed to update reward currency. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      print('🛑 Done: turning off loading');
      isLoading.value = false;
    }
  }
}